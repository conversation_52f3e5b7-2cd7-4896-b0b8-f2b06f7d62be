using System.Diagnostics;
using System.Windows;
using System.Windows.Navigation;

namespace EmailClient.Views;

public partial class GoogleSetupHelpWindow : Window
{
    public GoogleSetupHelpWindow()
    {
        InitializeComponent();
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
    {
        try
        {
            Process.Start(new ProcessStartInfo
            {
                FileName = e.Uri.AbsoluteUri,
                UseShellExecute = true
            });
        }
        catch
        {
            // Ignore errors opening browser
        }
        e.Handled = true;
    }
}
