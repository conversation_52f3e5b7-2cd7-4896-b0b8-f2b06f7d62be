2025-07-09 17:18:04.589 -07:00 [ERR] Fatal error during application startup
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at EmailClient.App.OnStartup(StartupEventArgs e) in C:\Users\<USER>\dev\windows app\EmailClient\App.xaml.cs:line 33
2025-07-09 17:18:05.122 -07:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-09 17:18:05.127 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-09 17:18:05.134 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-09 17:18:05.143 -07:00 [INF] Applying migration '20250709225018_AddGoogleIntegration'.
2025-07-09 17:18:05.189 -07:00 [ERR] Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "EmailAddress" TEXT NOT NULL,
    "ImapServer" TEXT NOT NULL,
    "ImapPort" INTEGER NOT NULL,
    "UseSsl" INTEGER NOT NULL,
    "Username" TEXT NOT NULL,
    "Password" TEXT NOT NULL,
    "IsEnabled" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL
);
2025-07-09 17:18:14.104 -07:00 [ERR] Fatal error during application startup
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at EmailClient.App.OnStartup(StartupEventArgs e) in C:\Users\<USER>\dev\windows app\EmailClient\App.xaml.cs:line 33
2025-07-09 17:18:14.638 -07:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-07-09 17:18:14.649 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-07-09 17:18:27.372 -07:00 [ERR] Fatal error during application startup
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at EmailClient.App.OnStartup(StartupEventArgs e) in C:\Users\<USER>\dev\windows app\EmailClient\App.xaml.cs:line 33
2025-07-09 17:24:30.122 -07:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 17:24:31.313 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 17:24:31.379 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__accountId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Folders" AS "f"
WHERE "f"."AccountId" = @__accountId_0
ORDER BY "f"."Type", "f"."Name"
2025-07-09 17:24:31.461 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 17:24:31.465 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 17:24:31.485 -07:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 17:24:31.538 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 17:24:32.578 -07:00 [INF] Starting sync service
2025-07-09 17:24:32.584 -07:00 [INF] Starting background sync
2025-07-09 17:24:32.629 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 17:24:32.633 -07:00 [INF] Starting sync <NAME_EMAIL>
2025-07-09 17:24:33.749 -07:00 [WRN] Failed to get counts for folder AI
MailKit.Net.Imap.ImapCommandException: The IMAP server replied to the 'EXAMINE' command with a 'NO' response: Unknown Mailbox: AI (Failure)
   at MailKit.Net.Imap.ImapCommand.ThrowIfNotOk(String command)
   at MailKit.Net.Imap.ImapFolder.ProcessOpenResponse(ImapCommand ic, FolderAccess access)
   at MailKit.Net.Imap.ImapFolder.OpenAsync(ImapCommand ic, FolderAccess access)
   at EmailClient.Services.ImapService.GetFoldersAsync(EmailAccount account) in C:\Users\<USER>\dev\windows app\EmailClient\Services\ImapService.cs:line 73
2025-07-09 17:24:34.698 -07:00 [WRN] Failed to get counts for folder Assistant
MailKit.Net.Imap.ImapCommandException: The IMAP server replied to the 'EXAMINE' command with a 'NO' response: Unknown Mailbox: Assistant (Failure)
   at MailKit.Net.Imap.ImapCommand.ThrowIfNotOk(String command)
   at MailKit.Net.Imap.ImapFolder.ProcessOpenResponse(ImapCommand ic, FolderAccess access)
   at MailKit.Net.Imap.ImapFolder.OpenAsync(ImapCommand ic, FolderAccess access)
   at EmailClient.Services.ImapService.GetFoldersAsync(EmailAccount account) in C:\Users\<USER>\dev\windows app\EmailClient\Services\ImapService.cs:line 73
2025-07-09 17:24:35.815 -07:00 [WRN] Failed to get counts for folder [Gmail]
MailKit.Net.Imap.ImapCommandException: The IMAP server replied to the 'EXAMINE' command with a 'NO' response: Unknown Mailbox: [Gmail] (Failure)
   at MailKit.Net.Imap.ImapCommand.ThrowIfNotOk(String command)
   at MailKit.Net.Imap.ImapFolder.ProcessOpenResponse(ImapCommand ic, FolderAccess access)
   at MailKit.Net.Imap.ImapFolder.OpenAsync(ImapCommand ic, FolderAccess access)
   at EmailClient.Services.ImapService.GetFoldersAsync(EmailAccount account) in C:\Users\<USER>\dev\windows app\EmailClient\Services\ImapService.cs:line 73
2025-07-09 17:24:37.269 -07:00 [WRN] Failed to get counts for folder [Superhuman]/AI
MailKit.Net.Imap.ImapCommandException: The IMAP server replied to the 'EXAMINE' command with a 'NO' response: Unknown Mailbox: [Superhuman]/AI (Failure)
   at MailKit.Net.Imap.ImapCommand.ThrowIfNotOk(String command)
   at MailKit.Net.Imap.ImapFolder.ProcessOpenResponse(ImapCommand ic, FolderAccess access)
   at MailKit.Net.Imap.ImapFolder.OpenAsync(ImapCommand ic, FolderAccess access)
   at EmailClient.Services.ImapService.GetFoldersAsync(EmailAccount account) in C:\Users\<USER>\dev\windows app\EmailClient\Services\ImapService.cs:line 73
2025-07-09 17:24:38.943 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__account_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Folders" AS "f"
WHERE "f"."AccountId" = @__account_Id_0
2025-07-09 17:24:39.005 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.008 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.008 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.008 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.008 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.008 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.009 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.009 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.009 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.009 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.009 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.009 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.009 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.010 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.010 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.010 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.010 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.010 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.010 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.010 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.011 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p2='?' (DbType = Int32), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0, "TotalCount" = @p1
WHERE "Id" = @p2
RETURNING 1;
2025-07-09 17:24:39.011 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.011 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.011 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.011 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.011 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.011 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.012 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.013 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Folders" SET "LastSyncAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:39.026 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__accountId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Folders" AS "f"
WHERE "f"."AccountId" = @__accountId_0
2025-07-09 17:24:39.028 -07:00 [INF] Syncing messages for folder AI
2025-07-09 17:24:39.928 -07:00 [ERR] Failed to sync messages for folder AI
MailKit.Net.Imap.ImapCommandException: The IMAP server replied to the 'EXAMINE' command with a 'NO' response: Unknown Mailbox: AI (Failure)
   at MailKit.Net.Imap.ImapFolder.OpenAsync(ImapCommand ic, FolderAccess access)
   at EmailClient.Services.ImapService.GetMessagesAsync(EmailAccount account, EmailFolder folder, Int32 limit) in C:\Users\<USER>\dev\windows app\EmailClient\Services\ImapService.cs:line 93
   at EmailClient.Services.EmailService.SyncFolderMessagesAsync(EmailAccount account, EmailFolder folder) in C:\Users\<USER>\dev\windows app\EmailClient\Services\EmailService.cs:line 131
2025-07-09 17:24:39.929 -07:00 [INF] Syncing messages for folder AI-Processed
2025-07-09 17:24:40.791 -07:00 [INF] Sync completed for folder AI-Processed: 0 new, 0 updated
2025-07-09 17:24:40.791 -07:00 [INF] Syncing messages for folder Important-NotUrgent
2025-07-09 17:24:42.390 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:42.397 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsFlagged" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:42.401 -07:00 [INF] Sync completed for folder Important-NotUrgent: 0 new, 1 updated
2025-07-09 17:24:42.401 -07:00 [INF] Syncing messages for folder Needs-Review
2025-07-09 17:24:43.361 -07:00 [INF] Sync completed for folder Needs-Review: 0 new, 0 updated
2025-07-09 17:24:43.361 -07:00 [INF] Syncing messages for folder NotImportant-NotUrgent
2025-07-09 17:24:44.276 -07:00 [INF] Sync completed for folder NotImportant-NotUrgent: 0 new, 0 updated
2025-07-09 17:24:44.276 -07:00 [INF] Syncing messages for folder Urgent-Important
2025-07-09 17:24:45.286 -07:00 [INF] Sync completed for folder Urgent-Important: 0 new, 0 updated
2025-07-09 17:24:45.286 -07:00 [INF] Syncing messages for folder Urgent-NotImportant
2025-07-09 17:24:46.286 -07:00 [INF] Sync completed for folder Urgent-NotImportant: 0 new, 0 updated
2025-07-09 17:24:46.286 -07:00 [INF] Syncing messages for folder Assistant
2025-07-09 17:24:47.265 -07:00 [ERR] Failed to sync messages for folder Assistant
MailKit.Net.Imap.ImapCommandException: The IMAP server replied to the 'EXAMINE' command with a 'NO' response: Unknown Mailbox: Assistant (Failure)
   at MailKit.Net.Imap.ImapFolder.OpenAsync(ImapCommand ic, FolderAccess access)
   at EmailClient.Services.ImapService.GetMessagesAsync(EmailAccount account, EmailFolder folder, Int32 limit) in C:\Users\<USER>\dev\windows app\EmailClient\Services\ImapService.cs:line 93
   at EmailClient.Services.EmailService.SyncFolderMessagesAsync(EmailAccount account, EmailFolder folder) in C:\Users\<USER>\dev\windows app\EmailClient\Services\EmailService.cs:line 131
2025-07-09 17:24:47.266 -07:00 [INF] Syncing messages for folder Financial
2025-07-09 17:24:50.444 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.444 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.446 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.446 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.446 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.447 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.447 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.447 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.448 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.448 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.448 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.449 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.449 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.449 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:50.450 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p2='?' (DbType = Int32), @p0='?' (DbType = Boolean), @p1='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsFlagged" = @p0, "IsRead" = @p1
WHERE "Id" = @p2
RETURNING 1;
2025-07-09 17:24:50.450 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.450 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.451 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.452 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:50.455 -07:00 [INF] Sync completed for folder Financial: 0 new, 12 updated
2025-07-09 17:24:50.455 -07:00 [INF] Syncing messages for folder High-Importance
2025-07-09 17:24:52.262 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:52.262 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:52.263 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:52.263 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:52.263 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:52.264 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:52.264 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:52.266 -07:00 [INF] Sync completed for folder High-Importance: 0 new, 3 updated
2025-07-09 17:24:52.266 -07:00 [INF] Syncing messages for folder Meeting
2025-07-09 17:24:53.888 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:53.888 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:53.888 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:53.888 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:53.888 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:53.891 -07:00 [INF] Sync completed for folder Meeting: 0 new, 2 updated
2025-07-09 17:24:53.891 -07:00 [INF] Syncing messages for folder Personal
2025-07-09 17:24:57.709 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.710 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.710 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.710 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.710 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.711 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.711 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.711 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.711 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.711 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.711 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.711 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 2)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.712 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.712 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.712 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.712 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.712 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.712 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.712 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.713 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.713 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.714 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.715 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.715 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.715 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.715 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.715 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.715 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:57.718 -07:00 [INF] Sync completed for folder Personal: 0 new, 15 updated
2025-07-09 17:24:57.718 -07:00 [INF] Syncing messages for folder Urgent
2025-07-09 17:24:59.240 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:59.241 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals1_account_Id_0='?' (DbType = Int32), @__message_MessageId_1='?' (Size = 1)], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses"
FROM "Messages" AS "m"
WHERE "m"."AccountId" = @__8__locals1_account_Id_0 AND "m"."MessageId" = @__message_MessageId_1
LIMIT 1
2025-07-09 17:24:59.241 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:59.241 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Messages" SET "IsRead" = @p0
WHERE "Id" = @p1
RETURNING 1;
2025-07-09 17:24:59.244 -07:00 [INF] Sync completed for folder Urgent: 0 new, 2 updated
2025-07-09 17:24:59.244 -07:00 [INF] Syncing messages for folder INBOX
