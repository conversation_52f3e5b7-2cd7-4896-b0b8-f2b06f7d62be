<Window x:Class="EmailClient.Views.GoogleSetupHelpWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Google Cloud Setup Guide" Height="700" Width="900"
        WindowStartupLocation="CenterOwner">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <TextBlock Text="Google Cloud Setup Guide" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>
            
            <TextBlock Text="Follow these steps to configure Google Cloud for your email client:" 
                       FontSize="14" Margin="0,0,0,15" TextWrapping="Wrap"/>
            
            <!-- Step 1 -->
            <Border BorderBrush="LightGray" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="Step 1: Create Google Cloud Project" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        1. Go to <Hyperlink NavigateUri="https://console.cloud.google.com" RequestNavigate="Hyperlink_RequestNavigate">Google Cloud Console</Hyperlink>
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        2. Click "Select a project" → "New Project"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        3. Enter project name (e.g., "Email Client Integration")
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap">
                        4. Click "Create"
                    </TextBlock>
                </StackPanel>
            </Border>
            
            <!-- Step 2 -->
            <Border BorderBrush="LightGray" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="Step 2: Enable Required APIs" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        1. In the Google Cloud Console, go to "APIs &amp; Services" → "Library"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        2. Search for and enable these APIs:
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5">
                        • Google People API (for contacts)
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5">
                        • Google Calendar API (for calendar events)
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5">
                        • Google+ API (for user profile)
                    </TextBlock>
                </StackPanel>
            </Border>
            
            <!-- Step 3 -->
            <Border BorderBrush="LightGray" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="Step 3: Configure OAuth Consent Screen" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        1. Go to "APIs &amp; Services" → "OAuth consent screen"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        2. Choose "External" user type (unless you have Google Workspace)
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        3. Fill in required fields:
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5">
                        • App name: "Email Client"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5">
                        • User support email: Your email
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5">
                        • Developer contact: Your email
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        4. Add scopes: email, profile, contacts.readonly, calendar.readonly
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap">
                        5. Add your email as a test user
                    </TextBlock>
                </StackPanel>
            </Border>
            
            <!-- Step 4 -->
            <Border BorderBrush="LightGray" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="Step 4: Create OAuth 2.0 Credentials" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        1. Go to "APIs &amp; Services" → "Credentials"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        3. Choose "Web application" as application type (NOT Desktop application)
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        4. Enter name: "Email Client Web"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        5. Click "Create"
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" FontWeight="Bold" Foreground="Red">
                        6. Copy the Client ID and Client Secret - you'll need these!
                    </TextBlock>
                </StackPanel>
            </Border>
            
            <!-- Step 5 -->
            <Border BorderBrush="LightGray" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="Step 5: Configure Redirect URIs (Important!)" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        1. In the OAuth 2.0 Client ID you just created, click the edit button
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        2. Under "Authorized redirect URIs", add:
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5" FontFamily="Consolas" Background="LightGray" Padding="5">
                        http://localhost:8080/
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="20,0,0,5" FontFamily="Consolas" Background="LightGray" Padding="5">
                        http://127.0.0.1:8080/
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        3. Click "Save"
                    </TextBlock>
                </StackPanel>
            </Border>
            
            <!-- Troubleshooting -->
            <Border BorderBrush="Orange" BorderThickness="2" Padding="15" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="Common Issues &amp; Solutions" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                    
                    <TextBlock Text="Error: 'redirect_uri_mismatch'" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        Solution: Make sure you added http://localhost:8080/ and http://127.0.0.1:8080/ to authorized redirect URIs and used Web application type
                    </TextBlock>
                    
                    <TextBlock Text="Error: 'access_denied'" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        Solution: Add your email as a test user in OAuth consent screen
                    </TextBlock>
                    
                    <TextBlock Text="Error: 'invalid_client'" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        Solution: Double-check your Client ID and Client Secret are correct
                    </TextBlock>
                    
                    <TextBlock Text="Error: 'API not enabled'" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        Solution: Enable Google People API and Google Calendar API in the API Library
                    </TextBlock>
                </StackPanel>
            </Border>
            
            <Button Content="Close" Click="CloseButton_Click" Width="100" HorizontalAlignment="Right" Margin="0,20,0,0"/>
        </StackPanel>
    </ScrollViewer>
</Window>
