<Window x:Class="EmailClient.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Settings" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Settings" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Settings Content -->
        <TabControl Grid.Row="1">
            <!-- General Tab -->
            <TabItem Header="General">
                <StackPanel Margin="10">
                    <!-- Sync Settings -->
                    <GroupBox Header="Synchronization" Margin="0,0,0,15">
                        <StackPanel Margin="10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Grid.Column="0" Content="Sync Interval:" VerticalAlignment="Center"/>
                                <Slider Grid.Column="1" 
                                        Value="{Binding SyncIntervalMinutes}" 
                                        Minimum="5" Maximum="60" 
                                        TickFrequency="5" 
                                        IsSnapToTickEnabled="True"
                                        Margin="10,0"/>
                                <TextBlock Grid.Column="2" 
                                           Text="{Binding SyncIntervalDisplay}" 
                                           VerticalAlignment="Center" 
                                           MinWidth="80"/>
                            </Grid>
                            
                            <CheckBox Content="Enable automatic sync" 
                                      IsChecked="{Binding AutoSyncEnabled}" 
                                      Margin="0,10,0,0"/>
                            
                            <CheckBox Content="Sync on startup" 
                                      IsChecked="{Binding SyncOnStartup}" 
                                      Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- Display Settings -->
                    <GroupBox Header="Display" Margin="0,0,0,15">
                        <StackPanel Margin="10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Grid.Column="0" Content="Messages per page:" VerticalAlignment="Center"/>
                                <ComboBox Grid.Column="1" 
                                          SelectedValue="{Binding MessagesPerPage}"
                                          Margin="10,0,0,0" Width="100" HorizontalAlignment="Left">
                                    <ComboBoxItem Content="25" Tag="25"/>
                                    <ComboBoxItem Content="50" Tag="50"/>
                                    <ComboBoxItem Content="100" Tag="100"/>
                                    <ComboBoxItem Content="200" Tag="200"/>
                                </ComboBox>
                            </Grid>
                            
                            <CheckBox Content="Show message preview" 
                                      IsChecked="{Binding ShowMessagePreview}" 
                                      Margin="0,10,0,0"/>
                            
                            <CheckBox Content="Mark as read when selected" 
                                      IsChecked="{Binding MarkReadOnSelect}" 
                                      Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </TabItem>
            
            <!-- Accounts Tab -->
            <TabItem Header="Accounts">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Account List -->
                    <ListView Grid.Row="0" ItemsSource="{Binding Accounts}" SelectedItem="{Binding SelectedAccount}">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Name" Width="150" DisplayMemberBinding="{Binding Name}"/>
                                <GridViewColumn Header="Email" Width="200" DisplayMemberBinding="{Binding EmailAddress}"/>
                                <GridViewColumn Header="Server" Width="150" DisplayMemberBinding="{Binding ImapServer}"/>
                                <GridViewColumn Header="Enabled" Width="80">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox IsChecked="{Binding IsEnabled}" HorizontalAlignment="Center"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                            </GridView>
                        </ListView.View>
                    </ListView>
                    
                    <!-- Account Buttons -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                        <Button Content="Add Account" Command="{Binding AddAccountCommand}" Margin="0,0,10,0"/>
                        <Button Content="Edit Account" Command="{Binding EditAccountCommand}" 
                                IsEnabled="{Binding SelectedAccount, Converter={StaticResource NullToVisibilityConverter}}" 
                                Margin="0,0,10,0"/>
                        <Button Content="Remove Account" Command="{Binding RemoveAccountCommand}" 
                                IsEnabled="{Binding SelectedAccount, Converter={StaticResource NullToVisibilityConverter}}"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Google Integration Tab -->
            <TabItem Header="Google Integration">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- Authentication Section -->
                        <GroupBox Margin="0,0,0,15">
                            <GroupBox.Header>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="Authentication" VerticalAlignment="Center"/>
                                    <Button Content="Setup Help"
                                            Command="{Binding ShowGoogleSetupHelpCommand}"
                                            Margin="10,0,0,0"
                                            Padding="5,2"
                                            FontSize="10"/>
                                </StackPanel>
                            </GroupBox.Header>
                            <StackPanel Margin="10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Label Grid.Row="0" Grid.Column="0" Content="Client ID:" VerticalAlignment="Center"/>
                                    <TextBox Grid.Row="0" Grid.Column="1"
                                             Text="{Binding GoogleClientId}"
                                             Margin="10,0,0,5"/>

                                    <Label Grid.Row="1" Grid.Column="0" Content="Client Secret:" VerticalAlignment="Center"/>
                                    <PasswordBox Grid.Row="1" Grid.Column="1"
                                                 x:Name="GoogleClientSecretBox"
                                                 Margin="10,0,0,5"/>

                                    <Label Grid.Row="2" Grid.Column="0" Content="Status:" VerticalAlignment="Center"/>
                                    <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,5">
                                        <TextBlock Text="{Binding GoogleAuthStatus}" VerticalAlignment="Center"/>
                                        <Button Content="Validate Config"
                                                Command="{Binding ValidateGoogleConfigCommand}"
                                                Margin="10,0,0,0"
                                                IsEnabled="{Binding CanAuthenticateGoogle}"/>
                                        <Button Content="Authenticate"
                                                Command="{Binding AuthenticateGoogleCommand}"
                                                Margin="10,0,0,0"
                                                IsEnabled="{Binding CanAuthenticateGoogle}"/>
                                        <Button Content="Revoke"
                                                Command="{Binding RevokeGoogleAuthCommand}"
                                                Margin="10,0,0,0"
                                                IsEnabled="{Binding IsGoogleAuthenticated}"/>
                                    </StackPanel>

                                    <Label Grid.Row="3" Grid.Column="0" Content="User:" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1"
                                               Text="{Binding GoogleUserInfo}"
                                               VerticalAlignment="Center"
                                               Margin="10,0,0,5"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                        <!-- Contacts Sync Section -->
                        <GroupBox Header="Contacts Synchronization" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <CheckBox Content="Enable contacts synchronization"
                                          IsChecked="{Binding ContactsSyncEnabled}"
                                          Margin="0,0,0,10"/>

                                <Grid IsEnabled="{Binding ContactsSyncEnabled}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Label Grid.Row="0" Grid.Column="0" Content="Sync Interval:" VerticalAlignment="Center"/>
                                    <Slider Grid.Row="0" Grid.Column="1"
                                            Value="{Binding ContactsSyncIntervalMinutes}"
                                            Minimum="15" Maximum="1440"
                                            TickFrequency="15"
                                            IsSnapToTickEnabled="True"
                                            Margin="10,0"/>
                                    <TextBlock Grid.Row="0" Grid.Column="2"
                                               Text="{Binding ContactsSyncIntervalDisplay}"
                                               VerticalAlignment="Center"
                                               MinWidth="80"/>

                                    <Label Grid.Row="1" Grid.Column="0" Content="Last Sync:" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1"
                                               Text="{Binding LastContactsSync}"
                                               VerticalAlignment="Center"
                                               Margin="10,0,0,5"/>
                                    <Button Grid.Row="1" Grid.Column="2"
                                            Content="Sync Now"
                                            Command="{Binding SyncContactsCommand}"
                                            IsEnabled="{Binding IsGoogleAuthenticated}"/>

                                    <Label Grid.Row="2" Grid.Column="0" Content="Contacts Count:" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1"
                                               Text="{Binding ContactsCount}"
                                               VerticalAlignment="Center"
                                               Margin="10,0,0,5"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                        <!-- Calendar Sync Section -->
                        <GroupBox Header="Calendar Synchronization" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <CheckBox Content="Enable calendar synchronization"
                                          IsChecked="{Binding CalendarSyncEnabled}"
                                          Margin="0,0,0,10"/>

                                <Grid IsEnabled="{Binding CalendarSyncEnabled}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Label Grid.Row="0" Grid.Column="0" Content="Sync Interval:" VerticalAlignment="Center"/>
                                    <Slider Grid.Row="0" Grid.Column="1"
                                            Value="{Binding CalendarSyncIntervalMinutes}"
                                            Minimum="15" Maximum="1440"
                                            TickFrequency="15"
                                            IsSnapToTickEnabled="True"
                                            Margin="10,0"/>
                                    <TextBlock Grid.Row="0" Grid.Column="2"
                                               Text="{Binding CalendarSyncIntervalDisplay}"
                                               VerticalAlignment="Center"
                                               MinWidth="80"/>

                                    <Label Grid.Row="1" Grid.Column="0" Content="Last Sync:" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1"
                                               Text="{Binding LastCalendarSync}"
                                               VerticalAlignment="Center"
                                               Margin="10,0,0,5"/>
                                    <Button Grid.Row="1" Grid.Column="2"
                                            Content="Sync Now"
                                            Command="{Binding SyncCalendarCommand}"
                                            IsEnabled="{Binding IsGoogleAuthenticated}"/>

                                    <Label Grid.Row="2" Grid.Column="0" Content="Events Count:" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1"
                                               Text="{Binding CalendarEventsCount}"
                                               VerticalAlignment="Center"
                                               Margin="10,0,0,5"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="OK" 
                    Command="{Binding SaveCommand}"
                    IsDefault="True" 
                    Width="75" Margin="0,0,10,0"/>
            <Button Content="Cancel" 
                    Command="{Binding CancelCommand}"
                    IsCancel="True" 
                    Width="75"/>
        </StackPanel>
    </Grid>
</Window>
